// app.js
const { getWithTTL, setWithTTL } = require('./utils/storage');

const AGREEMENT_KEY = 'userAgreementAccepted';
const AGREEMENT_TTL = 365 * 24 * 60 * 60 * 1000; // 1年

App({
  onLaunch: function () {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 检查用户协议同意状态
    this.checkAgreementStatus();
  },

  // 检查协议同意状态
  checkAgreementStatus: function() {
    const agreed = getWithTTL(AGREEMENT_KEY);
    console.log('[privacy] checkAgreementStatus agreed =', agreed);
    if (agreed) return;

    // 优先走微信官方隐私授权流程
    if (wx.getPrivacySetting) {
      wx.getPrivacySetting({
        success: (res) => {
          console.log('[privacy] getPrivacySetting:', res);
          if (res.needAuthorization) {
            if (wx.openPrivacyContract) {
              wx.openPrivacyContract({
                success: () => {
                  console.log('[privacy] user opened privacy contract');
                  setWithTTL(AGREEMENT_KEY, true, AGREEMENT_TTL);
                  this.showPhoneAuthModal();
                },
                fail: (err) => {
                  console.log('[privacy] openPrivacyContract fail', err);
                  this.showAgreementModal();
                }
              });
            } else {
              // 低版本fallback
              this.showAgreementModal();
            }
          } else {
            // needAuthorization=false：根据平台已同意或未配置，但业务仍需明确同意
            // 若本地未有同意标记，则弹出自定义协议
            this.showAgreementModal();
          }
        },
        fail: () => {
          // 调用失败，fallback到自定义弹窗
          this.showAgreementModal();
        }
      });
    } else {
      // 无隐私API，fallback
      this.showAgreementModal();
    }
  },

  // 显示协议弹窗
  showAgreementModal: function() {
    wx.showModal({
      title: '用户协议与隐私政策',
      content: '欢迎使用AWX应用！\n\n为了向您提供更好的服务，我们需要获取您的手机号进行身份验证。\n\n请您仔细阅读并同意《用户协议》和《隐私政策》后继续使用。',
      confirmText: '同意并继续',
      cancelText: '暂不同意',
      success: (res) => {
        if (res.confirm) {
          // 用户同意协议，缓存状态并弹出手机号授权
          setWithTTL(AGREEMENT_KEY, true, AGREEMENT_TTL);
          this.showPhoneAuthModal();
        } else {
          // 用户不同意，可以继续使用但功能受限
          wx.showToast({
            title: '您可以在设置中重新同意协议',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  // 显示手机号授权弹窗
  showPhoneAuthModal: function() {
    wx.showModal({
      title: '手机号授权',
      content: '为了提供更好的服务体验，我们需要获取您的手机号进行身份验证。',
      confirmText: '授权手机号',
      cancelText: '暂不授权',
      success: (res) => {
        if (res.confirm) {
          // 跳转到登录页进行手机号授权
          wx.navigateTo({
            url: '/pages/login/login?autoAuth=true'
          });
        }
      }
    });
  },

  globalData: {
    userInfo: null
  }
})