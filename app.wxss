/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
} 

/* 全局文本样式 */
text {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 
                Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
                sans-serif;
}

/* 主题色 */
.primary-color {
  color: #1296db;
}

.primary-bg {
  background-color: #1296db;
}

/* 通用边距 */
.margin-sm {
  margin: 10rpx;
}

.margin {
  margin: 20rpx;
}

.margin-lg {
  margin: 40rpx;
}

.padding-sm {
  padding: 10rpx;
}

.padding {
  padding: 20rpx;
}

.padding-lg {
  padding: 40rpx;
}

/* 通用flex布局 */
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

/* 通用字体大小 */
.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-df {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-xxl {
  font-size: 44rpx;
}

/* 通用按钮样式 */
.btn-primary {
  background-color: #1296db;
  color: white;
}

.btn-default {
  background-color: #f8f8f8;
  color: #333333;
}