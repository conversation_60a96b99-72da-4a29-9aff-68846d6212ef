# 底部导航栏图标

请在此目录中添加以下图标文件，用于底部导航栏：

1. `home.png` - 首页图标（未选中状态）
2. `home_selected.png` - 首页图标（选中状态）
3. `product.png` - 产品图标（未选中状态）
4. `product_selected.png` - 产品图标（选中状态）
5. `solution.png` - 解决方案图标（未选中状态）
6. `solution_selected.png` - 解决方案图标（选中状态）
7. `news.png` - 新闻图标（未选中状态）
8. `news_selected.png` - 新闻图标（选中状态）
9. `profile.png` - 个人中心图标（未选中状态）
10. `profile_selected.png` - 个人中心图标（选中状态）

## 图标要求

- 建议尺寸：81px × 81px
- 格式：PNG
- 透明背景
- 未选中状态通常使用灰色
- 选中状态通常使用主题色（当前配置为 #1296db）

您可以使用设计工具创建这些图标，或从图标库中下载适合的图标。

## 注意事项

确保图标文件名与 app.json 中配置的路径一致，否则图标将无法正确显示。