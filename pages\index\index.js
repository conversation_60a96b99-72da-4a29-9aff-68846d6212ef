// pages/index/index.js
const { getWithTTL } = require('../../utils/storage');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 轮播图数据
    banners: [
      {
        id: 1,
        imageUrl: '/static/images/banner1.png',
        title: '全球支付及金融平台',
        subtitle: '助力企业无界增长'
      },
      {
        id: 2,
        imageUrl: '/static/images/banner2.png',
        title: '全球企业账户',
        subtitle: '高效支付与财务运营'
      }
    ],
    // 产品数据
    products: [
      {
        id: 1,
        icon: '/static/images/global-account.png',
        title: '全球企业账户',
        desc: '高效支付与财务运营',
        imageUrl: '/static/images/product-preview.png'
      }
    ],
    // 客户案例数据
    clients: [
      { id: 1, name: 'SHEIN', logo: '/static/images/clients/shein.png' },
      { id: 2, name: 'BIGO LIVE', logo: '/static/images/clients/bigo.png' },
      { id: 3, name: 'POIZON', logo: '/static/images/clients/poizon.png' },
      { id: 4, name: '网易游戏', logo: '/static/images/clients/netease.png' },
      { id: 5, name: 'GLP', logo: '/static/images/clients/glp.png' },
      { id: 6, name: 'TikTok', logo: '/static/images/clients/tiktok.png' },
      { id: 7, name: '腾讯音乐娱乐集团', logo: '/static/images/clients/tme.png' },
      { id: 8, name: 'MeetSocial Group', logo: '/static/images/clients/meetsocial.png' }
    ],
    isLoggedIn: false
  },

  onShow: function () {
    // 在页面显示时检查并触发用户协议弹窗，确保能看到
    const app = getApp();
    const AGREEMENT_KEY = 'userAgreementAccepted';
    const agreed = getWithTTL(AGREEMENT_KEY);
    if (!agreed && app && typeof app.showAgreementModal === 'function') {
      console.log('[privacy] force showAgreementModal from index.onShow');
      app.showAgreementModal();
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查用户是否已登录
    const token = wx.getStorageSync('token');
    if (token) {
      this.setData({
        isLoggedIn: true
      });
      // TODO: 验证token有效性
      // this.validateToken(token);
    }
    
    // TODO: 获取首页数据
    // this.fetchHomeData();
  },

  /**
   * 验证Token有效性
   */
  validateToken: function(token) {
    // TODO: 调用验证token接口
    // wx.request({
    //   url: 'API_URL/validateToken',
    //   method: 'POST',
    //   header: {
    //     'Authorization': `Bearer ${token}`
    //   },
    //   success: (res) => {
    //     if (!res.data.success) {
    //       // token无效，清除登录状态
    //       wx.removeStorageSync('token');
    //       wx.removeStorageSync('userInfo');
    //       this.setData({
    //         isLoggedIn: false
    //       });
    //     }
    //   },
    //   fail: () => {
    //     console.error('验证token失败');
    //   }
    // });
  },

  /**
   * 获取首页数据
   */
  fetchHomeData: function() {
    // TODO: 调用获取首页数据接口
    // wx.request({
    //   url: 'API_URL/home',
    //   method: 'GET',
    //   success: (res) => {
    //     if (res.data.success) {
    //       this.setData({
    //         banners: res.data.banners || this.data.banners,
    //         products: res.data.products || this.data.products,
    //         clients: res.data.clients || this.data.clients
    //       });
    //     }
    //   },
    //   fail: () => {
    //     console.error('获取首页数据失败');
    //   }
    // });
  },

  /**
   * 跳转到登录页面
   */
  navigateToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 跳转到产品详情页
   */
  navigateToProduct: function(e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product/detail?id=${productId}`
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: 'AWX - 全球支付及金融平台',
      path: '/pages/index/index'
    };
  }
})