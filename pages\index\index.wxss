.container {
  padding-bottom: 120rpx;
}

/* 轮播图样式 */
.banner {
  width: 100%;
  height: 400rpx;
  position: relative;
}

.banner image {
  width: 100%;
  height: 100%;
}

.banner-text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 40rpx;
  background: linear-gradient(rgba(0,0,0,0.1), rgba(0,0,0,0.3));
}

.banner-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.banner-subtitle {
  font-size: 32rpx;
  color: #ffffff;
  margin-bottom: 40rpx;
}

.banner-action {
  width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333333;
  border-radius: 35rpx;
  font-size: 28rpx;
}

/* 产品介绍样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  padding: 30rpx 30rpx 20rpx;
}

.product-card {
  margin: 0 30rpx;
  background-color: #ff6b00;
  border-radius: 20rpx;
  padding: 30rpx;
  color: #ffffff;
  display: flex;
  position: relative;
  overflow: hidden;
  height: 300rpx;
}

.product-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.product-icon image {
  width: 100%;
  height: 100%;
}

.product-content {
  flex: 1;
}

.product-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.product-desc {
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.product-more {
  font-size: 24rpx;
  display: flex;
  align-items: center;
}

.arrow {
  margin-left: 10rpx;
}

.product-image {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 300rpx;
  height: 200rpx;
}

.product-image image {
  width: 100%;
  height: 100%;
}

/* 客户案例样式 */
.client-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx;
}

.client-item {
  width: 25%;
  height: 120rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.client-item image {
  max-width: 100%;
  max-height: 100%;
}