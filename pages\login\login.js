// pages/login/login.js
const { request } = require('../../utils/request');
const { setWithTTL, getWithTTL, remove } = require('../../utils/storage');
const { encryptPhoneWithRSA } = require('../../utils/rsaEncrypt');
const { H5_REGISTER_BASE_URL } = require('../../utils/config');

const SESSION_KEY = 'userSession';
const TOKEN_KEY = 'token';
const TTL_30D = 30 * 24 * 60 * 60 * 1000; // 30天

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isRegister: false,  // 是否为注册模式
    phone: '',          // 手机号
    password: '',       // 密码
    confirmPassword: '', // 确认密码
    verificationCode: '', // 验证码
    counting: false,    // 是否在倒计时
    countDown: 60,      // 倒计时秒数
    agreeProtocol: false, // 是否同意协议
    isIOS: false,       // 是否为iOS设备
    loggedIn: false,    // 是否已登录（新流程）
    userInfo: null      // 本地缓存的用户信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检测是否为iOS设备
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          isIOS: res.platform === 'ios'
        });
      }
    });
    this.syncSessionFromCache();
    
    // 检查是否从app.js自动跳转过来进行手机号授权
    if (options && options.autoAuth === 'true') {
      // 延迟一下确保页面渲染完成
      setTimeout(() => {
        this.triggerPhoneAuth();
      }, 500);
    }
  },

  onShow: function () {
    this.syncSessionFromCache();
  },

  syncSessionFromCache: function () {
    const session = getWithTTL(SESSION_KEY);
    if (session && session.token) {
      this.setData({ loggedIn: true, userInfo: session.userInfo || null });
    } else {
      this.setData({ loggedIn: false, userInfo: null });
    }
  },

  logoutNewFlow: function () {
    wx.showModal({
      title: '确认退出',
      content: '退出后需要重新登录',
      success: (res) => {
        if (res.confirm) {
          try { remove(SESSION_KEY); } catch (e) {}
          try { wx.removeStorageSync(TOKEN_KEY); } catch (e) {}
          this.setData({ loggedIn: false, userInfo: null });
          wx.showToast({ title: '已退出登录', icon: 'none' });
        }
      }
    });
  },

  // 新流程：手机号授权入口（需要开放能力）
  // 触发手机号授权（用于从app.js自动跳转）
  triggerPhoneAuth: function() {
    // 模拟点击手机号授权按钮
    wx.showModal({
      title: '手机号授权',
      content: '点击确定将弹出微信手机号授权',
      success: (res) => {
        if (res.confirm) {
          // 这里需要用户手动点击按钮，因为微信不允许程序自动触发授权
          wx.showToast({
            title: '请点击下方"手机号登录"按钮',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  onGetPhoneNumber: function (e) {
    if (e.detail.errMsg && !e.detail.errMsg.includes('ok')) {
      wx.showToast({ title: '未获取到手机号授权', icon: 'none' });
      return;
    }
    
    // 从授权结果中获取手机号（这里需要根据实际授权返回的数据结构调整）
    // 如果微信返回的是加密数据，需要后端解密；如果返回明文，直接使用
    let phone = '';
    
    // 方案1：如果微信返回明文手机号
    if (e.detail.phoneNumber) {
      phone = e.detail.phoneNumber;
    }
    // 方案2：如果微信返回加密数据，需要后端解密
    else if (e.detail.code) {
      // 这里需要调用后端接口解密获取手机号
      wx.showLoading({ title: '获取手机号中...' });
      request({
        url: '/auth2/decodePhone',
        method: 'POST',
        data: { code: e.detail.code }
      }).then(res => {
        wx.hideLoading();
        if (res && res.phone) {
          phone = res.phone;
          this.processPhoneAuth(phone);
        } else {
          wx.showToast({ title: '获取手机号失败', icon: 'none' });
        }
      }).catch(() => {
        wx.hideLoading();
        wx.showToast({ title: '网络错误', icon: 'none' });
      });
      return;
    }
    // 方案3：从输入框获取（临时方案）
    else {
      phone = this.data.phone;
      if (!phone || phone.length !== 11) {
        wx.showToast({ title: '请输入正确的手机号', icon: 'none' });
        return;
      }
    }
    
    this.processPhoneAuth(phone);
  },

  // 处理手机号授权后的逻辑
  processPhoneAuth: function(phone) {
    const phoneEncrypted = encryptPhoneWithRSA(phone);
    if (!phoneEncrypted) {
      wx.showToast({ title: '加密失败', icon: 'none' });
      return;
    }
    this.obtainUnionId().then(unionId => {
      this.verifyRegisterStatus({ unionId, phoneEncrypted });
    }).catch(() => {
      wx.showToast({ title: '获取UnionID失败', icon: 'none' });
    });
  },

  // 第2步：调用后端校验注册状态（占位，等待接口细节）
  verifyRegisterStatus: function ({ unionId, phoneEncrypted }) {
    wx.showLoading({ title: '验证中...' });
    request({
      url: '/auth2/verify',
      method: 'POST',
      data: {
        unionId,
        phoneEncrypted,
        algo: 'RSA2048_PKCS1_v1_5_BASE64'
      }
    }).then(res => {
      wx.hideLoading();
      if (res && res.registered) {
        const token = res.token;
        const userInfo = res.userInfo || {};
        setWithTTL(SESSION_KEY, { token, unionId, encPhone: phoneEncrypted, userInfo }, TTL_30D);
        this.setData({ loggedIn: true, userInfo });
        wx.showToast({ title: '登录成功', icon: 'success' });
        setTimeout(() => { wx.switchTab({ url: '/pages/index/index' }); }, 800);
      } else {
        // 未注册：缓存临时token并打开H5
        const tempToken = (res && res.token) || '';
        const h5Url = (res && res.h5Url) || '';
        wx.setStorageSync('tempRegisterToken', tempToken);
        const url = h5Url || this.composeRegisterUrl({ token: tempToken, unionId, encPhone: phoneEncrypted });
        wx.navigateTo({ url: `/pages/webview/webview?url=${encodeURIComponent(url)}` });
      }
    }).catch(() => {
      wx.hideLoading();
      wx.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
    });
  },

  obtainUnionId: function () {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (!res.code) { reject(); return; }
          // 这里通常需要后端换取 unionId。若后端另有接口，请替换为真实调用。
          request({ url: '/auth2/unionid', method: 'POST', data: { code: res.code } })
            .then(r => {
              if (r && r.unionId) resolve(r.unionId); else reject();
            })
            .catch(() => reject());
        },
        fail: reject
      });
    });
  },

  composeRegisterUrl: function ({ token, unionId, encPhone }) {
    // 若后端未返回 h5Url，则前端拼接，基址来自配置
    const baseUrl = H5_REGISTER_BASE_URL;
    const params = `token=${encodeURIComponent(token)}&unionId=${encodeURIComponent(unionId)}&encPhone=${encodeURIComponent(encPhone)}`;
    return `${baseUrl}?${params}`;
  },

  /**
   * 输入手机号
   */
  inputPhone: function(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  /**
   * 输入密码
   */
  inputPassword: function(e) {
    this.setData({
      password: e.detail.value
    });
  },

  /**
   * 输入确认密码
   */
  inputConfirmPassword: function(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  /**
   * 输入验证码
   */
  inputVerificationCode: function(e) {
    this.setData({
      verificationCode: e.detail.value
    });
  },

  /**
   * 切换登录/注册模式
   */
  switchLoginRegister: function() {
    this.setData({
      isRegister: !this.data.isRegister,
      // 切换模式时清空输入
      password: '',
      confirmPassword: '',
      verificationCode: ''
    });
  },

  /**
   * 获取验证码
   */
  getVerificationCode: function() {
    // 验证手机号
    if (!this.data.phone || this.data.phone.length !== 11) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    request({
      url: '/auth1/sendCode',
      method: 'POST',
      data: { phone: this.data.phone }
    }).then(res => {
      wx.showToast({ title: res.msg, icon: res.success ? 'success' : 'none' });
      if (res.success) this.startCountDown();
    }).catch(() => {
      wx.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
    });
  },

  /**
   * 开始倒计时
   */
  startCountDown: function() {
    this.setData({
      counting: true,
      countDown: 60
    });

    const timer = setInterval(() => {
      if (this.data.countDown <= 1) {
        clearInterval(timer);
        this.setData({
          counting: false
        });
      } else {
        this.setData({
          countDown: this.data.countDown - 1
        });
      }
    }, 1000);
  },

  /**
   * 切换协议同意状态
   */
  toggleAgreement: function() {
    this.setData({
      agreeProtocol: !this.data.agreeProtocol
    });
  },

  /**
   * 显示用户协议
   */
  showUserAgreement: function() {
    // TODO: 跳转到用户协议页面
    wx.navigateTo({
      url: '/pages/agreement/user'
    });
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy: function() {
    // TODO: 跳转到隐私政策页面
    wx.navigateTo({
      url: '/pages/agreement/privacy'
    });
  },

  /**
   * 登录
   */
  login: function() {
    // 表单验证
    if (!this.data.phone || this.data.phone.length !== 11) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    if (!this.data.password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请阅读并同意用户协议和隐私政策',
        icon: 'none'
      });
      return;
    }
    wx.showLoading({ title: '登录中...' });
    request({
      url: '/auth1/login',
      method: 'POST',
      data: { phone: this.data.phone, password: this.data.password }
    }).then(res => {
      wx.hideLoading();
      if (res.success) {
        wx.setStorageSync('token', res.data);
        wx.showToast({ title: '登录成功', icon: 'success', duration: 1500 });
        setTimeout(() => {
          wx.switchTab({ url: '/pages/index/index' });
        }, 1500);
      } else {
        wx.showToast({ title: res.msg || '登录失败', icon: 'none' });
      }
    }).catch(() => {
      wx.hideLoading();
      wx.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
    });
  },

  /**
   * 注册
   */
  register: function() {
    // 表单验证
    if (!this.data.phone || this.data.phone.length !== 11) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    if (!this.data.password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    if (this.data.password !== this.data.confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      });
      return;
    }

    if (!this.data.verificationCode) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      });
      return;
    }

    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请阅读并同意用户协议和隐私政策',
        icon: 'none'
      });
      return;
    }
    wx.showLoading({ title: '注册中...' });
    request({
      url: '/auth1/register',
      method: 'POST',
      data: {
        phone: this.data.phone,
        password: this.data.password,
        confirmPassword: this.data.confirmPassword,
        verificationCode: this.data.verificationCode
      }
    }).then(res => {
      wx.hideLoading();
      wx.showToast({ title: res.msg, icon: res.success ? 'success' : 'none', duration: 1500 });
      if (res.success) {
        setTimeout(() => {
          this.setData({
            isRegister: false,
            password: '',
            confirmPassword: '',
            verificationCode: ''
          });
        }, 1500);
      }
    }).catch(() => {
      wx.hideLoading();
      wx.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
    });
  },

  /**
   * 微信登录
   */
  wechatLogin: function() {
    // TODO: 调用微信登录接口
    wx.login({
      success: (res) => {
        if (res.code) {
          // 获取到微信登录code后，发送到后端换取用户信息
          // wx.request({
          //   url: 'API_URL/wechatLogin',
          //   method: 'POST',
          //   data: {
          //     code: res.code
          //   },
          //   success: (res) => {
          //     if (res.data.success) {
          //       // 保存登录状态和token
          //       wx.setStorageSync('token', res.data.token);
          //       wx.setStorageSync('userInfo', res.data.userInfo);
          //       
          //       // 跳转到首页
          //       wx.switchTab({
          //         url: '/pages/index/index'
          //       });
          //     } else {
          //       wx.showToast({
          //         title: res.data.message || '微信登录失败',
          //         icon: 'none'
          //       });
          //     }
          //   },
          //   fail: () => {
          //     wx.showToast({
          //       title: '网络错误，请稍后重试',
          //       icon: 'none'
          //     });
          //   }
          // });
          
          // 模拟登录成功
          wx.showToast({
            title: '微信登录成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.switchTab({
                  url: '/pages/index/index'
                });
              }, 1500);
            }
          });
        } else {
          wx.showToast({
            title: '微信登录失败',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * Apple登录 (仅iOS设备)
   */
  appleLogin: function() {
    // TODO: 调用Apple登录接口
    // 由于微信小程序暂不支持直接调用Apple登录，这里仅作为示例
    wx.showToast({
      title: 'Apple登录功能开发中',
      icon: 'none'
    });
  }
})