<view class="container">
  <view class="logo-container">
    <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
    <text class="app-name">AWX应用</text>
  </view>
  
  <view class="form-container" wx:if="{{false}}">
    <view class="input-group">
      <view class="input-item">
        <text class="icon">📱</text>
        <input type="text" placeholder="请输入手机号" bindinput="inputPhone" value="{{phone}}" maxlength="11" />
      </view>
      
      <view class="input-item">
        <text class="icon">🔒</text>
        <input type="password" placeholder="请输入密码" bindinput="inputPassword" value="{{password}}" password="true" />
      </view>
      
      <view class="input-item" wx:if="{{isRegister}}">
        <text class="icon">🔑</text>
        <input type="password" placeholder="请确认密码" bindinput="inputConfirmPassword" value="{{confirmPassword}}" password="true" />
      </view>
      
      <view class="input-item verification" wx:if="{{isRegister}}">
        <text class="icon">📲</text>
        <input type="number" placeholder="请输入验证码" bindinput="inputVerificationCode" value="{{verificationCode}}" maxlength="6" />
        <button class="verification-btn {{counting ? 'counting' : ''}}" bindtap="getVerificationCode" disabled="{{counting}}">
          {{counting ? countDown + 's' : '获取验证码'}}
        </button>
      </view>
    </view>
    
    <view class="btn-group">
      <button class="primary-btn" bindtap="{{isRegister ? 'register' : 'login'}}">
        {{isRegister ? '注册' : '登录'}}
      </button>
      <view class="switch-action" bindtap="switchLoginRegister">
        {{isRegister ? '已有账号，去登录' : '没有账号？去注册'}}
      </view>
    </view>
    
    <view class="other-login">
      <view class="divider">
        <view class="line"></view>
        <text>其他登录方式</text>
        <view class="line"></view>
      </view>
      <view class="icon-group">
        <view class="icon-item" bindtap="wechatLogin">
          <image src="/static/images/wechat.png" mode="aspectFit"></image>
        </view>
        <view class="icon-item" bindtap="appleLogin" wx:if="{{isIOS}}">
          <image src="/static/images/apple.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>
  
  <view class="agreement" wx:if="{{!loggedIn}}">
    <checkbox checked="{{agreeProtocol}}" bindtap="toggleAgreement"></checkbox>
    <text class="agreement-text">我已阅读并同意<text class="link" bindtap="showUserAgreement">《用户协议》</text>和<text class="link" bindtap="showPrivacyPolicy">《隐私政策》</text></text>
  </view>

  <view wx:if="{{!loggedIn}}" style="width:100%;">
    <button type="primary" open-type="getPhoneNumber" bindgetphonenumber="onGetPhoneNumber">手机号登录</button>
  </view>

  <view wx:if="{{loggedIn}}" style="width:100%;margin-top:40rpx;">
    <view style="text-align:center;color:#333;">
      已登录
    </view>
    <view wx:if="{{userInfo}}" style="margin-top:10rpx;text-align:center;color:#666;">
      {{userInfo.nickName || userInfo.phone || ''}}
    </view>
    <button style="margin-top:20rpx;" type="warn" bindtap="logoutNewFlow">退出登录</button>
  </view>
</view>