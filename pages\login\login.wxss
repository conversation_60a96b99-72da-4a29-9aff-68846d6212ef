.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  height: 100vh;
  background-color: #ffffff;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 60rpx;
  margin-bottom: 60rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-top: 20rpx;
  color: #333333;
}

.form-container {
  width: 100%;
}

.input-group {
  margin-bottom: 40rpx;
}

.input-item {
  display: flex;
  align-items: center;
  height: 100rpx;
  border-bottom: 1rpx solid #eeeeee;
  margin-bottom: 20rpx;
}

.icon {
  margin-right: 20rpx;
  font-size: 40rpx;
}

input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.verification {
  position: relative;
}

.verification-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  background-color: #ff6b00;
  color: white;
  border-radius: 30rpx;
  min-width: 160rpx;
  height: 60rpx;
  line-height: 40rpx;
}

.verification-btn.counting {
  background-color: #cccccc;
}

.btn-group {
  margin-top: 60rpx;
}

.primary-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #ff6b00;
  color: white;
  font-size: 32rpx;
  border-radius: 45rpx;
}

.switch-action {
  text-align: center;
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666666;
}

.other-login {
  margin-top: 80rpx;
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.line {
  width: 100rpx;
  height: 1rpx;
  background-color: #dddddd;
}

.divider text {
  margin: 0 20rpx;
  font-size: 24rpx;
  color: #999999;
}

.icon-group {
  display: flex;
  justify-content: center;
}

.icon-item {
  width: 80rpx;
  height: 80rpx;
  margin: 0 30rpx;
}

.icon-item image {
  width: 100%;
  height: 100%;
}

.agreement {
  position: fixed;
  bottom: 60rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.agreement-text {
  color: #666666;
}

.link {
  color: #ff6b00;
}