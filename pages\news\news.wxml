<view class="container">
  <view class="header">
    <view class="title">行业资讯</view>
    <view class="subtitle">了解最新的支付和金融科技动态</view>
  </view>
  
  <!-- 资讯分类标签 -->
  <scroll-view class="category-scroll" scroll-x="true" enhanced="true" show-scrollbar="false">
    <view class="category-list">
      <view class="category-item {{currentCategory === 'all' ? 'active' : ''}}" bindtap="switchCategory" data-category="all">全部</view>
      <view class="category-item {{currentCategory === 'industry' ? 'active' : ''}}" bindtap="switchCategory" data-category="industry">行业动态</view>
      <view class="category-item {{currentCategory === 'product' ? 'active' : ''}}" bindtap="switchCategory" data-category="product">产品更新</view>
      <view class="category-item {{currentCategory === 'regulation' ? 'active' : ''}}" bindtap="switchCategory" data-category="regulation">政策法规</view>
      <view class="category-item {{currentCategory === 'case' ? 'active' : ''}}" bindtap="switchCategory" data-category="case">案例分析</view>
    </view>
  </scroll-view>
  
  <!-- 资讯列表 -->
  <view class="news-list">
    <!-- 置顶资讯 -->
    <view class="top-news" wx:if="{{topNews}}" bindtap="navigateToDetail" data-id="{{topNews.id}}">
      <image class="top-image" src="{{topNews.coverImage}}" mode="aspectFill"></image>
      <view class="top-info">
        <view class="top-tag">置顶</view>
        <view class="top-title">{{topNews.title}}</view>
        <view class="top-meta">
          <text class="top-date">{{topNews.date}}</text>
          <text class="top-views">{{topNews.views}} 阅读</text>
        </view>
      </view>
    </view>
    
    <!-- 普通资讯列表 -->
    <view class="news-item" wx:for="{{newsList}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
      <view class="news-content">
        <view class="news-title">{{item.title}}</view>
        <view class="news-desc">{{item.summary}}</view>
        <view class="news-meta">
          <text class="news-category">{{item.categoryName}}</text>
          <text class="news-date">{{item.date}}</text>
        </view>
      </view>
      <view class="news-image" wx:if="{{item.coverImage}}">
        <image src="{{item.coverImage}}" mode="aspectFill"></image>
      </view>
    </view>
  </view>
  
  <!-- 加载更多 -->
  <view class="loading-more" wx:if="{{hasMore}}">
    <view class="loading-text" bindtap="loadMore">加载更多</view>
  </view>
  <view class="no-more" wx:else>
    <view class="no-more-text">没有更多内容了</view>
  </view>
</view>