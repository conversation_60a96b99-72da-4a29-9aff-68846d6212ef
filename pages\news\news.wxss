.container {
  padding-bottom: 120rpx;
}

.header {
  padding: 40rpx;
  background-color: #f8f8f8;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666666;
}

/* 分类标签样式 */
.category-scroll {
  width: 100%;
  white-space: nowrap;
  background-color: #ffffff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eeeeee;
}

.category-list {
  padding: 0 20rpx;
  display: flex;
}

.category-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666666;
  border-radius: 30rpx;
  transition: all 0.3s;
}

.category-item.active {
  background-color: #ff6b00;
  color: #ffffff;
}

/* 置顶资讯样式 */
.top-news {
  margin: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

.top-image {
  width: 100%;
  height: 320rpx;
}

.top-info {
  padding: 20rpx;
}

.top-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #ffffff;
  background-color: #ff6b00;
  padding: 6rpx 16rpx;
  border-radius: 6rpx;
  margin-bottom: 16rpx;
}

.top-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.top-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999999;
}

/* 普通资讯列表样式 */
.news-list {
  padding: 0 30rpx;
}

.news-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eeeeee;
}

.news-content {
  flex: 1;
  margin-right: 20rpx;
}

.news-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 16rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-meta {
  display: flex;
  font-size: 24rpx;
  color: #999999;
}

.news-category {
  margin-right: 20rpx;
  color: #ff6b00;
}

.news-image {
  width: 180rpx;
  height: 140rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.news-image image {
  width: 100%;
  height: 100%;
}

/* 加载更多样式 */
.loading-more, .no-more {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text, .no-more-text {
  font-size: 26rpx;
  color: #999999;
}

.loading-text {
  color: #ff6b00;
}