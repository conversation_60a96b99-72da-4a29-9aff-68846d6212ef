// pages/product/product.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    products: [
      {
        id: 1,
        icon: '/static/images/global-account.png',
        title: '全球企业账户',
        desc: '高效支付与财务运营',
        features: [
          '一站式开立多币种账户',
          '支持全球收款和付款',
          '实时汇率转换',
          '低成本跨境转账'
        ]
      },
      {
        id: 2,
        icon: '/static/images/payment.png',
        title: '全球支付解决方案',
        desc: '覆盖200+国家和地区',
        features: [
          '支持100+支付方式',
          '本地化支付体验',
          '智能支付路由',
          '高转化率支付流程'
        ]
      },
      {
        id: 3,
        icon: '/static/images/finance.png',
        title: '企业金融服务',
        desc: '灵活的资金管理方案',
        features: [
          '贸易融资',
          '供应链金融',
          '跨境结算',
          '外汇风险管理'
        ]
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // TODO: 获取产品列表数据
    // this.fetchProductList();
  },

  /**
   * 获取产品列表
   */
  fetchProductList: function() {
    // TODO: 调用获取产品列表接口
    // wx.request({
    //   url: 'API_URL/products',
    //   method: 'GET',
    //   success: (res) => {
    //     if (res.data.success) {
    //       this.setData({
    //         products: res.data.products || this.data.products
    //       });
    //     }
    //   },
    //   fail: () => {
    //     console.error('获取产品列表失败');
    //   }
    // });
  },

  /**
   * 跳转到产品详情页
   */
  navigateToDetail: function(e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product/detail?id=${productId}`
    });
  },

  /**
   * 联系我们
   */
  contactUs: function() {
    wx.navigateTo({
      url: '/pages/contact/contact'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: 'AWX - 产品介绍',
      path: '/pages/product/product'
    };
  }
})