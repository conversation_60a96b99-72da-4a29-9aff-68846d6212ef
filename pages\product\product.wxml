<view class="container">
  <view class="header">
    <view class="title">产品介绍</view>
    <view class="subtitle">为全球企业提供一站式金融解决方案</view>
  </view>
  
  <view class="product-list">
    <view class="product-item" wx:for="{{products}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
      <view class="product-icon">
        <image src="{{item.icon}}" mode="aspectFit"></image>
      </view>
      <view class="product-content">
        <view class="product-title">{{item.title}}</view>
        <view class="product-desc">{{item.desc}}</view>
        <view class="product-features">
          <view class="feature-item" wx:for="{{item.features}}" wx:for-item="feature" wx:key="index">
            <text class="feature-dot">•</text>
            <text>{{feature}}</text>
          </view>
        </view>
      </view>
      <view class="product-arrow">
        <text>→</text>
      </view>
    </view>
  </view>
  
  <view class="contact-section">
    <view class="contact-title">需要更多产品信息？</view>
    <view class="contact-desc">联系我们的客户经理获取详细解决方案</view>
    <button class="contact-btn" bindtap="contactUs">联系我们</button>
  </view>
</view>