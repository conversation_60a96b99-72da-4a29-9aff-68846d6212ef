.container {
  padding-bottom: 120rpx;
}

.header {
  padding: 60rpx 40rpx;
  background-color: #f8f8f8;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666666;
}

.product-list {
  padding: 20rpx 30rpx;
}

.product-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eeeeee;
  position: relative;
}

.product-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.product-icon image {
  width: 100%;
  height: 100%;
}

.product-content {
  flex: 1;
}

.product-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.product-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.product-features {
  margin-top: 10rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  font-size: 24rpx;
  color: #888888;
  margin-bottom: 6rpx;
}

.feature-dot {
  margin-right: 8rpx;
  color: #ff6b00;
}

.product-arrow {
  display: flex;
  align-items: center;
  color: #ff6b00;
  font-size: 32rpx;
}

.contact-section {
  margin-top: 60rpx;
  padding: 40rpx;
  background-color: #f8f8f8;
  text-align: center;
}

.contact-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.contact-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 30rpx;
}

.contact-btn {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #ff6b00;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 40rpx;
}