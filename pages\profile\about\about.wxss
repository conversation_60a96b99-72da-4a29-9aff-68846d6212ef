.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
  background-color: #ffffff;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.app-name {
  font-size: 36rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.version {
  font-size: 24rpx;
  color: #999999;
}

.info-list {
  background-color: #ffffff;
  margin: 20rpx 0;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #333333;
}

.info-value {
  font-size: 28rpx;
  color: #666666;
}

.info-arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #cccccc;
  border-right: 2rpx solid #cccccc;
  transform: rotate(45deg);
}

.company-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  padding-bottom: 60rpx;
}

.company-name {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.copyright {
  font-size: 24rpx;
  color: #999999;
}