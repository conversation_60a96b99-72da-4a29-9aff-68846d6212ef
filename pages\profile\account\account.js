// pages/profile/account/account.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    avatarUrl: '',
    nickname: '',
    gender: 0, // 0: 未知, 1: 男, 2: 女
    genderOptions: ['未设置', '男', '女'],
    birthday: '',
    region: ['', '', ''],
    customItem: '全部'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        avatarUrl: userInfo.avatar || '/static/images/avatar-placeholder.png',
        nickname: userInfo.nickname || userInfo.username || '',
        gender: userInfo.gender || 0,
        birthday: userInfo.birthday || '',
        region: userInfo.region || ['', '', '']
      });
    } else {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        success: () => {
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      });
    }
  },

  /**
   * 选择头像
   */
  chooseAvatar: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.setData({
          avatarUrl: tempFilePath
        });
        
        // TODO: 上传头像到服务器
        // this.uploadAvatar(tempFilePath);
        
        // 模拟上传成功
        wx.showToast({
          title: '头像更新成功',
          icon: 'success'
        });
        
        // 更新本地存储
        const userInfo = this.data.userInfo;
        userInfo.avatar = tempFilePath;
        wx.setStorageSync('userInfo', userInfo);
      }
    });
  },
  
  /**
   * 上传头像到服务器
   */
  uploadAvatar: function(filePath) {
    // TODO: 实现头像上传逻辑
    // wx.uploadFile({
    //   url: 'API_URL/uploadAvatar',
    //   filePath: filePath,
    //   name: 'avatar',
    //   header: {
    //     'Authorization': `Bearer ${wx.getStorageSync('token')}`
    //   },
    //   success: (res) => {
    //     const data = JSON.parse(res.data);
    //     if (data.success) {
    //       // 更新本地存储
    //       const userInfo = this.data.userInfo;
    //       userInfo.avatar = data.avatarUrl;
    //       wx.setStorageSync('userInfo', userInfo);
    //       
    //       wx.showToast({
    //         title: '头像更新成功',
    //         icon: 'success'
    //       });
    //     } else {
    //       wx.showToast({
    //         title: '头像更新失败',
    //         icon: 'none'
    //       });
    //     }
    //   },
    //   fail: () => {
    //     wx.showToast({
    //       title: '头像上传失败',
    //       icon: 'none'
    //     });
    //   }
    // });
  },
  
  /**
   * 输入昵称
   */
  inputNickname: function(e) {
    this.setData({
      nickname: e.detail.value
    });
  },
  
  /**
   * 选择性别
   */
  bindGenderChange: function(e) {
    this.setData({
      gender: parseInt(e.detail.value)
    });
  },
  
  /**
   * 选择生日
   */
  bindBirthdayChange: function(e) {
    this.setData({
      birthday: e.detail.value
    });
  },
  
  /**
   * 选择地区
   */
  bindRegionChange: function(e) {
    this.setData({
      region: e.detail.value
    });
  },
  
  /**
   * 保存个人资料
   */
  saveProfile: function() {
    if (!this.data.nickname.trim()) {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }
    
    const userInfo = this.data.userInfo;
    userInfo.nickname = this.data.nickname;
    userInfo.gender = this.data.gender;
    userInfo.birthday = this.data.birthday;
    userInfo.region = this.data.region;
    
    // TODO: 调用保存个人资料接口
    // wx.request({
    //   url: 'API_URL/updateProfile',
    //   method: 'POST',
    //   header: {
    //     'Authorization': `Bearer ${wx.getStorageSync('token')}`
    //   },
    //   data: {
    //     nickname: userInfo.nickname,
    //     gender: userInfo.gender,
    //     birthday: userInfo.birthday,
    //     region: userInfo.region
    //   },
    //   success: (res) => {
    //     if (res.data.success) {
    //       wx.setStorageSync('userInfo', userInfo);
    //       wx.showToast({
    //         title: '保存成功',
    //         icon: 'success'
    //       });
    //     } else {
    //       wx.showToast({
    //         title: res.data.message || '保存失败',
    //         icon: 'none'
    //       });
    //     }
    //   },
    //   fail: () => {
    //     wx.showToast({
    //       title: '网络错误，请重试',
    //       icon: 'none'
    //     });
    //   }
    // });
    
    // 模拟保存成功
    wx.setStorageSync('userInfo', userInfo);
    wx.showToast({
      title: '保存成功',
      icon: 'success',
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  }
})