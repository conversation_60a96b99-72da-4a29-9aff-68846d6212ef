<view class="container">
  <view class="form-group">
    <view class="form-item avatar-item">
      <view class="form-label">头像</view>
      <view class="avatar-wrapper" bindtap="chooseAvatar">
        <image class="avatar" src="{{avatarUrl}}" mode="aspectFill"></image>
        <view class="avatar-edit">更换</view>
      </view>
    </view>
    
    <view class="form-item">
      <view class="form-label">昵称</view>
      <input class="form-input" value="{{nickname}}" bindinput="inputNickname" placeholder="请输入昵称" maxlength="20" />
    </view>
    
    <view class="form-item">
      <view class="form-label">性别</view>
      <picker class="form-picker" bindchange="bindGenderChange" value="{{gender}}" range="{{genderOptions}}">
        <view class="picker-value">{{genderOptions[gender]}}</view>
      </picker>
    </view>
    
    <view class="form-item">
      <view class="form-label">生日</view>
      <picker class="form-picker" mode="date" bindchange="bindBirthdayChange" value="{{birthday}}">
        <view class="picker-value">{{birthday || '请选择生日'}}</view>
      </picker>
    </view>
    
    <view class="form-item">
      <view class="form-label">地区</view>
      <picker class="form-picker" mode="region" bindchange="bindRegionChange" value="{{region}}" custom-item="{{customItem}}">
        <view class="picker-value">{{region[0] ? region[0] + ' ' + region[1] + ' ' + region[2] : '请选择地区'}}</view>
      </picker>
    </view>
  </view>
  
  <view class="save-btn" bindtap="saveProfile">保存</view>
</view>