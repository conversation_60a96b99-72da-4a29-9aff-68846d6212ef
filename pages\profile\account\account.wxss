.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx 0;
}

.form-group {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333333;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.form-picker {
  flex: 1;
}

.picker-value {
  font-size: 28rpx;
  color: #333333;
}

/* 头像样式 */
.avatar-item {
  height: 160rpx;
}

.avatar-wrapper {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
}

.avatar {
  width: 100%;
  height: 100%;
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  font-size: 20rpx;
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.5);
}

/* 保存按钮 */
.save-btn {
  margin: 60rpx 30rpx;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background-color: #ff6b00;
  color: #ffffff;
  font-size: 30rpx;
  border-radius: 45rpx;
}