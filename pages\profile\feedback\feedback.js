// pages/profile/feedback/feedback.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    feedbackTypes: ['功能建议', '产品问题', '使用体验', '其他'],
    selectedType: 0,
    content: '',
    contactInfo: '',
    images: [],
    submitting: false
  },

  /**
   * 选择反馈类型
   */
  bindTypeChange: function(e) {
    this.setData({
      selectedType: parseInt(e.detail.value)
    });
  },
  
  /**
   * 输入反馈内容
   */
  inputContent: function(e) {
    this.setData({
      content: e.detail.value
    });
  },
  
  /**
   * 输入联系方式
   */
  inputContactInfo: function(e) {
    this.setData({
      contactInfo: e.detail.value
    });
  },
  
  /**
   * 选择图片
   */
  chooseImage: function() {
    const currentCount = this.data.images.length;
    const remainCount = 3 - currentCount;
    
    if (remainCount <= 0) {
      wx.showToast({
        title: '最多上传3张图片',
        icon: 'none'
      });
      return;
    }
    
    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        this.setData({
          images: [...this.data.images, ...tempFilePaths]
        });
      }
    });
  },
  
  /**
   * 预览图片
   */
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    wx.previewImage({
      current: this.data.images[index],
      urls: this.data.images
    });
  },
  
  /**
   * 删除图片
   */
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.images;
    images.splice(index, 1);
    this.setData({
      images: images
    });
  },
  
  /**
   * 提交反馈
   */
  submitFeedback: function() {
    if (!this.data.content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }
    
    this.setData({
      submitting: true
    });
    
    // TODO: 上传图片和提交反馈内容
    // 模拟提交过程
    setTimeout(() => {
      this.setData({
        submitting: false
      });
      
      wx.showToast({
        title: '提交成功',
        icon: 'success',
        success: () => {
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      });
    }, 1500);
  }
})