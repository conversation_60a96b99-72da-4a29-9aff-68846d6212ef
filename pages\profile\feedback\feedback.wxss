.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx 0;
}

.feedback-form {
  background-color: #ffffff;
  margin-bottom: 30rpx;
}

.form-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
}

.form-picker {
  width: 100%;
}

.picker-value {
  font-size: 28rpx;
  color: #333333;
}

.form-input {
  width: 100%;
  font-size: 28rpx;
  color: #333333;
}

.content-item {
  position: relative;
}

.feedback-content {
  width: 100%;
  height: 240rpx;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}

.content-counter {
  position: absolute;
  bottom: 30rpx;
  right: 30rpx;
  font-size: 24rpx;
  color: #999999;
}

.image-uploader {
  width: 100%;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.upload-icon {
  font-size: 48rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999999;
}

.upload-tips {
  font-size: 24rpx;
  color: #999999;
  margin-top: 10rpx;
}

.submit-btn {
  margin: 60rpx 30rpx;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background-color: #ff6b00;
  color: #ffffff;
  font-size: 30rpx;
  border-radius: 45rpx;
}

.submitting {
  opacity: 0.7;
}

.feedback-tips {
  padding: 0 30rpx;
  margin-bottom: 60rpx;
}

.tips-title {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.tips-content {
  padding-left: 20rpx;
}

.tips-item {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.8;
}