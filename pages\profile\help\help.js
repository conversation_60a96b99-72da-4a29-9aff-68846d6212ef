// pages/profile/help/help.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    helpList: [
      {
        id: 1,
        title: '如何注册账号？',
        content: '打开小程序，点击"我的"页面，选择"登录/注册"按钮，按照提示完成注册流程即可。'
      },
      {
        id: 2,
        title: '如何修改个人资料？',
        content: '登录后，进入"我的"页面，点击"账户设置"，可以修改头像、昵称等个人资料。'
      },
      {
        id: 3,
        title: '如何找回密码？',
        content: '在登录页面点击"忘记密码"，可以通过绑定的手机号或邮箱进行密码重置。'
      },
      {
        id: 4,
        title: '如何查看产品详情？',
        content: '在"产品"页面，点击感兴趣的产品卡片，即可查看产品的详细信息。'
      },
      {
        id: 5,
        title: '如何联系客服？',
        content: '在"我的"页面，点击"意见反馈"，可以填写您的问题并提交，我们的客服会尽快与您联系。'
      }
    ],
    activeId: -1
  },

  /**
   * 展开/收起问题
   */
  toggleQuestion: function(e) {
    const id = e.currentTarget.dataset.id;
    this.setData({
      activeId: this.data.activeId === id ? -1 : id
    });
  },
  
  /**
   * 联系客服
   */
  contactService: function() {
    wx.makePhoneCall({
      phoneNumber: '************',
      fail: () => {
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  }
})