.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx 0;
}

.help-list {
  background-color: #ffffff;
  margin-bottom: 30rpx;
}

.help-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.help-item:last-child {
  border-bottom: none;
}

.help-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.help-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.help-icon {
  width: 24rpx;
  height: 24rpx;
  position: relative;
}

.help-icon::before, .help-icon::after {
  content: '';
  position: absolute;
  background-color: #999999;
  transition: all 0.3s;
}

.help-icon::before {
  width: 24rpx;
  height: 2rpx;
  top: 11rpx;
  left: 0;
}

.help-icon::after {
  width: 2rpx;
  height: 24rpx;
  left: 11rpx;
  top: 0;
}

.help-icon-active::after {
  transform: rotate(90deg);
  opacity: 0;
}

.help-content {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  padding-top: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s;
}

.help-content-show {
  padding-top: 20rpx;
  max-height: 300rpx;
}

.contact-section {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  text-align: center;
}

.contact-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 30rpx;
}

.contact-btn {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #ff6b00;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0 auto 30rpx;
}

.contact-info {
  font-size: 24rpx;
  color: #999999;
}