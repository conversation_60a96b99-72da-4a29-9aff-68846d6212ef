// pages/profile/profile.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoggedIn: false,
    userInfo: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次页面显示时检查登录状态，以便及时更新
    this.checkLoginStatus();
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.setData({
        isLoggedIn: true,
        userInfo: userInfo
      });
      
      // TODO: 验证token有效性
      // this.validateToken(token);
    } else {
      this.setData({
        isLoggedIn: false,
        userInfo: null
      });
    }
  },

  /**
   * 验证Token有效性
   */
  validateToken: function(token) {
    // TODO: 调用验证token接口
    // wx.request({
    //   url: 'API_URL/validateToken',
    //   method: 'POST',
    //   header: {
    //     'Authorization': `Bearer ${token}`
    //   },
    //   success: (res) => {
    //     if (!res.data.success) {
    //       // token无效，清除登录状态
    //       wx.removeStorageSync('token');
    //       wx.removeStorageSync('userInfo');
    //       this.setData({
    //         isLoggedIn: false,
    //         userInfo: null
    //       });
    //     }
    //   },
    //   fail: () => {
    //     console.error('验证token失败');
    //   }
    // });
  },

  /**
   * 跳转到登录页面
   */
  navigateToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 跳转到账户设置页面
   */
  navigateToAccountSetting: function() {
    if (!this.data.isLoggedIn) {
      this.navigateToLogin();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/profile/account/account'
    });
  },

  /**
   * 跳转到安全中心页面
   */
  navigateToSecurity: function() {
    if (!this.data.isLoggedIn) {
      this.navigateToLogin();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/profile/security/security'
    });
  },

  /**
   * 跳转到帮助中心页面
   */
  navigateToHelp: function() {
    wx.navigateTo({
      url: '/pages/profile/help/help'
    });
  },

  /**
   * 跳转到意见反馈页面
   */
  navigateToFeedback: function() {
    wx.navigateTo({
      url: '/pages/profile/feedback/feedback'
    });
  },

  /**
   * 跳转到关于我们页面
   */
  navigateToAbout: function() {
    wx.navigateTo({
      url: '/pages/profile/about/about'
    });
  },

  /**
   * 退出登录
   */
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // TODO: 调用退出登录接口
          // wx.request({
          //   url: 'API_URL/logout',
          //   method: 'POST',
          //   header: {
          //     'Authorization': `Bearer ${wx.getStorageSync('token')}`
          //   },
          //   complete: () => {
          //     // 无论成功失败，都清除本地登录状态
          //     wx.removeStorageSync('token');
          //     wx.removeStorageSync('userInfo');
          //     this.setData({
          //       isLoggedIn: false,
          //       userInfo: null
          //     });
          //     
          //     wx.showToast({
          //       title: '已退出登录',
          //       icon: 'success'
          //     });
          //   }
          // });
          
          // 模拟退出登录
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          this.setData({
            isLoggedIn: false,
            userInfo: null
          });
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  }
})