<view class="container">
  <!-- 未登录状态 -->
  <view class="not-login" wx:if="{{!isLoggedIn}}">
    <image class="avatar-placeholder" src="/static/images/avatar-placeholder.png" mode="aspectFill"></image>
    <view class="login-tips">登录后体验更多功能</view>
    <button class="login-btn" bindtap="navigateToLogin">登录/注册</button>
  </view>
  
  <!-- 已登录状态 -->
  <view class="user-info" wx:else>
    <view class="user-header">
      <image class="user-bg" src="/static/images/profile-bg.jpg" mode="aspectFill"></image>
      <view class="user-avatar-container">
        <image class="user-avatar" src="{{userInfo.avatar || '/static/images/avatar-placeholder.png'}}" mode="aspectFill"></image>
        <view class="user-name">{{userInfo.nickname || userInfo.username}}</view>
        <view class="user-id">ID: {{userInfo.userId}}</view>
      </view>
    </view>
  </view>
  
  <!-- 功能菜单 -->
  <view class="menu-list">
    <view class="menu-group">
      <view class="menu-item" bindtap="navigateToAccountSetting">
        <view class="menu-icon account-icon"></view>
        <view class="menu-text">账户设置</view>
        <view class="menu-arrow"></view>
      </view>
      <view class="menu-item" bindtap="navigateToSecurity">
        <view class="menu-icon security-icon"></view>
        <view class="menu-text">安全中心</view>
        <view class="menu-arrow"></view>
      </view>
    </view>
    
    <view class="menu-group">
      <view class="menu-item" bindtap="navigateToHelp">
        <view class="menu-icon help-icon"></view>
        <view class="menu-text">帮助中心</view>
        <view class="menu-arrow"></view>
      </view>
      <view class="menu-item" bindtap="navigateToFeedback">
        <view class="menu-icon feedback-icon"></view>
        <view class="menu-text">意见反馈</view>
        <view class="menu-arrow"></view>
      </view>
      <view class="menu-item" bindtap="navigateToAbout">
        <view class="menu-icon about-icon"></view>
        <view class="menu-text">关于我们</view>
        <view class="menu-arrow"></view>
      </view>
    </view>
    
    <!-- 退出登录按钮 -->
    <view class="logout-btn" wx:if="{{isLoggedIn}}" bindtap="logout">退出登录</view>
  </view>
</view>