.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 未登录状态 */
.not-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  background-color: #ffffff;
}

.avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 30rpx;
}

.login-tips {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}

.login-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #ff6b00;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 40rpx;
}

/* 已登录状态 */
.user-info {
  position: relative;
}

.user-header {
  height: 300rpx;
  position: relative;
  overflow: hidden;
}

.user-bg {
  width: 100%;
  height: 100%;
  filter: blur(2px);
}

.user-avatar-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #ffffff;
  margin-bottom: 20rpx;
}

.user-name {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 功能菜单 */
.menu-list {
  padding: 20rpx 0;
}

.menu-group {
  margin-bottom: 20rpx;
  background-color: #ffffff;
}

.menu-item {
  display: flex;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.account-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDEyYzIuMjEgMCA0LTEuNzkgNC00cy0xLjc5LTQtNC00LTQgMS43OS00IDQgMS43OSA0IDQgNHptMCAyYy0yLjY3IDAtOCAxLjM0LTggNHYyaDE2di0yYzAtMi42Ni01LjMzLTQtOC00eiIgZmlsbD0iIzY2NiIvPjwvc3ZnPg==');
}

.security-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDFMMyA1djZjMCA1LjU1IDMuODQgMTAuNzQgOSAxMiA1LjE2LTEuMjYgOS02LjQ1IDktMTJWNWwtOS00em0wIDEwLjk5aDdjLS41MyA0LjEyLTMuMjggNy43OS03IDguOTRWMTJIMVY2LjNsOC0zLjRWMTJoM3YtLjAxeiIgZmlsbD0iIzY2NiIvPjwvc3ZnPg==');
}

.help-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTExIDEwaDJ2NGgtMnptMC02aDJ2NGgtMnptMCAxMmM1LjUyIDAgMTAtNC40OCAxMC0xMFMxNi41MiAyIDExIDIgMSA2LjQ4IDEgMTJzNC40OCAxMCAxMCAxMHptMC0xOGM0LjQyIDAgOCAzLjU4IDggOHMtMy41OCA4LTggOC04LTMuNTgtOC04IDMuNTgtOCA4LTh6IiBmaWxsPSIjNjY2Ii8+PC9zdmc+');
}

.feedback-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTIwIDJINGMtMS4xIDAtMS45OS45LTEuOTkgMkwyIDIybDQtNGgxNGMxLjEgMCAyLS45IDItMlY0YzAtMS4xLS45LTItMi0yem0tNyAxMmgtMnYtMmgydjJ6bTAtNGgtMlY2aDJ2NHoiIGZpbGw9IiM2NjYiLz48L3N2Zz4=');
}

.about-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bTEgMTVoLTJ2LTZoMnY2em0wLThoLTJ2LTJoMnYyeiIgZmlsbD0iIzY2NiIvPjwvc3ZnPg==');
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.menu-arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #cccccc;
  border-right: 2rpx solid #cccccc;
  transform: rotate(45deg);
}

/* 退出登录按钮 */
.logout-btn {
  margin: 60rpx 30rpx;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background-color: #ffffff;
  color: #ff6b00;
  font-size: 30rpx;
  border-radius: 45rpx;
}