// pages/profile/security/security.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    phone: '',
    email: '',
    isPasswordSet: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      // 处理手机号显示，中间4位用*代替
      let phone = userInfo.phone || '';
      if (phone && phone.length === 11) {
        phone = phone.substr(0, 3) + '****' + phone.substr(7);
      }
      
      // 处理邮箱显示，@前面部分用*代替一部分
      let email = userInfo.email || '';
      if (email && email.indexOf('@') > 1) {
        const parts = email.split('@');
        if (parts[0].length > 3) {
          email = parts[0].substr(0, 2) + '****' + parts[0].substr(-1) + '@' + parts[1];
        }
      }
      
      this.setData({
        userInfo: userInfo,
        phone: phone,
        email: email,
        isPasswordSet: !!userInfo.hasPassword
      });
    } else {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        success: () => {
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      });
    }
  },

  /**
   * 修改手机号
   */
  changePhone: function() {
    wx.navigateTo({
      url: '/pages/profile/security/phone/phone'
    });
  },
  
  /**
   * 修改邮箱
   */
  changeEmail: function() {
    wx.navigateTo({
      url: '/pages/profile/security/email/email'
    });
  },
  
  /**
   * 修改密码
   */
  changePassword: function() {
    wx.navigateTo({
      url: '/pages/profile/security/password/password'
    });
  }
})