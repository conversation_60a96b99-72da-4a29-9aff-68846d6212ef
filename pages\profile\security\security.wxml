<view class="container">
  <view class="security-list">
    <view class="security-item" bindtap="changePhone">
      <view class="security-info">
        <view class="security-title">手机号</view>
        <view class="security-value">{{phone || '未绑定'}}</view>
      </view>
      <view class="security-arrow"></view>
    </view>
    
    <view class="security-item" bindtap="changeEmail">
      <view class="security-info">
        <view class="security-title">邮箱</view>
        <view class="security-value">{{email || '未绑定'}}</view>
      </view>
      <view class="security-arrow"></view>
    </view>
    
    <view class="security-item" bindtap="changePassword">
      <view class="security-info">
        <view class="security-title">登录密码</view>
        <view class="security-value">{{isPasswordSet ? '已设置' : '未设置'}}</view>
      </view>
      <view class="security-arrow"></view>
    </view>
  </view>
  
  <view class="security-tips">
    <view class="tips-title">安全提示</view>
    <view class="tips-content">
      <view class="tips-item">1. 定期修改密码，提高账号安全性</view>
      <view class="tips-item">2. 绑定手机号和邮箱，便于账号找回</view>
      <view class="tips-item">3. 不要将账号信息泄露给他人</view>
    </view>
  </view>
</view>