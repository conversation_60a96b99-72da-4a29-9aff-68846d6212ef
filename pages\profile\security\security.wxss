.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx 0;
}

.security-list {
  background-color: #ffffff;
  margin-bottom: 30rpx;
}

.security-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.security-item:last-child {
  border-bottom: none;
}

.security-info {
  flex: 1;
}

.security-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.security-value {
  font-size: 24rpx;
  color: #999999;
}

.security-arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 2rpx solid #cccccc;
  border-right: 2rpx solid #cccccc;
  transform: rotate(45deg);
}

.security-tips {
  background-color: #ffffff;
  padding: 30rpx;
}

.tips-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.tips-content {
  padding-left: 20rpx;
}

.tips-item {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.8;
}