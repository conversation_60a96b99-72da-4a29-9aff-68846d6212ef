// pages/solution/solution.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    solutions: [
      {
        id: 1,
        icon: '/static/images/ecommerce.png',
        title: '跨境电商解决方案',
        desc: '为全球电商企业提供一站式支付与结算服务',
        tags: ['多币种收款', '本地支付方式', '智能风控', '资金结算']
      },
      {
        id: 2,
        icon: '/static/images/gaming.png',
        title: '游戏行业解决方案',
        desc: '为游戏开发商和发行商提供全球化支付体验',
        tags: ['全球收款', '虚拟物品交易', '防欺诈', '快速结算']
      },
      {
        id: 3,
        icon: '/static/images/travel.png',
        title: '旅游出行解决方案',
        desc: '为旅游企业提供跨境支付和外汇管理服务',
        tags: ['多币种支付', '实时汇率', '退款管理', '合规性']
      },
      {
        id: 4,
        icon: '/static/images/saas.png',
        title: 'SaaS服务解决方案',
        desc: '为SaaS企业提供订阅管理和全球收款服务',
        tags: ['订阅管理', '全球收款', '账单管理', '数据分析']
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // TODO: 获取解决方案列表数据
    // this.fetchSolutionList();
  },

  /**
   * 获取解决方案列表
   */
  fetchSolutionList: function() {
    // TODO: 调用获取解决方案列表接口
    // wx.request({
    //   url: 'API_URL/solutions',
    //   method: 'GET',
    //   success: (res) => {
    //     if (res.data.success) {
    //       this.setData({
    //         solutions: res.data.solutions || this.data.solutions
    //       });
    //     }
    //   },
    //   fail: () => {
    //     console.error('获取解决方案列表失败');
    //   }
    // });
  },

  /**
   * 跳转到解决方案详情页
   */
  navigateToDetail: function(e) {
    const solutionId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/solution/detail?id=${solutionId}`
    });
  },

  /**
   * 申请定制解决方案
   */
  requestCustomSolution: function() {
    wx.navigateTo({
      url: '/pages/contact/contact?type=custom_solution'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: 'AWX - 解决方案',
      path: '/pages/solution/solution'
    };
  }
})