<view class="container">
  <view class="header">
    <view class="title">解决方案</view>
    <view class="subtitle">针对不同行业的专业金融解决方案</view>
  </view>
  
  <view class="solution-list">
    <view class="solution-item" wx:for="{{solutions}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
      <view class="solution-icon">
        <image src="{{item.icon}}" mode="aspectFit"></image>
      </view>
      <view class="solution-content">
        <view class="solution-title">{{item.title}}</view>
        <view class="solution-desc">{{item.desc}}</view>
        <view class="solution-tag-list">
          <view class="solution-tag" wx:for="{{item.tags}}" wx:for-item="tag" wx:key="index">{{tag}}</view>
        </view>
      </view>
      <view class="solution-arrow">
        <text>→</text>
      </view>
    </view>
  </view>
  
  <view class="custom-solution">
    <view class="custom-title">需要定制解决方案？</view>
    <view class="custom-desc">我们的专业团队可以根据您的业务需求提供个性化方案</view>
    <button class="custom-btn" bindtap="requestCustomSolution">申请定制方案</button>
  </view>
</view>