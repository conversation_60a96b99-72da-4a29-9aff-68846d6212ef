.container {
  padding-bottom: 120rpx;
}

.header {
  padding: 60rpx 40rpx;
  background-color: #f8f8f8;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666666;
}

.solution-list {
  padding: 20rpx 30rpx;
}

.solution-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eeeeee;
  position: relative;
}

.solution-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.solution-icon image {
  width: 100%;
  height: 100%;
}

.solution-content {
  flex: 1;
}

.solution-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.solution-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.solution-tag-list {
  display: flex;
  flex-wrap: wrap;
}

.solution-tag {
  font-size: 22rpx;
  color: #ff6b00;
  background-color: rgba(255, 107, 0, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 10rpx;
}

.solution-arrow {
  display: flex;
  align-items: center;
  color: #ff6b00;
  font-size: 32rpx;
}

.custom-solution {
  margin-top: 60rpx;
  padding: 40rpx;
  background-color: #f8f8f8;
  text-align: center;
}

.custom-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.custom-desc {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 30rpx;
}

.custom-btn {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #ff6b00;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 40rpx;
}