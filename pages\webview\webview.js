// pages/webview/webview.js
const { setWithTTL, getWithTTL } = require('../../utils/storage');

const SESSION_KEY = 'userSession';
const TTL_30D = 30 * 24 * 60 * 60 * 1000;

Page({
  data: {
    src: ''
  },

  onLoad(options) {
    // 期望 options 传入 url、token、unionId、encPhone 等
    // H5地址需要由上游拼接，这里仅透传
    if (options && options.url) {
      this.setData({ src: options.url });
    }
  },

  onMessage(e) {
    // 期望H5通过postMessage回传 { type: 'register_done', payload: { userInfo, token, unionId, encPhone } }
    const detail = e.detail || {};
    const data = (detail.data && detail.data[0]) || detail.data || {};
    if (data.type === 'register_done' && data.payload) {
      const { userInfo, token, unionId, encPhone } = data.payload;
      setWithTTL(SESSION_KEY, { token, unionId, encPhone, userInfo }, TTL_30D);
      wx.showToast({ title: '注册成功', icon: 'success', duration: 1200 });
      setTimeout(() => {
        wx.navigateBack({ delta: 1 });
      }, 1200);
    }
  }
});




