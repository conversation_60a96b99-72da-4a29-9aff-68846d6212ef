/*
 Minimal JSEncrypt build for RSA PKCS1 v1.5 encryption in WeChat Mini Program.
 Source: https://github.com/travist/jsencrypt (MIT)
 Stripped to only what we need (RSA encrypt + public key load).
*/
/* eslint-disable */
var JSEncryptExports = {};
(function (exports) {
  function a(b){this.maxDigits=20;this.dpl10=15;this.ZERO=new c(0);this.ONE=new c(1);this.radix=16;this.biFromHex=function(b){var d=new c(0),e=b.length,f=0;for(var g=e-1;g>=0;g-=4){var h=parseInt(b.substring(Math.max(g-3,0),g+1),16);d.digits[f++]=h}d.isNeg=b.charAt(0)=='-';return d};this.biToHex=function(b){var d="",e=15,f;for(var g=this.biHighIndex(b);g>-1;g--)f=b.digits[g],d+=this.hexToChar[f>>4&15]+this.hexToChar[f&15];return d};this.biHighIndex=function(b){var d=b.digits.length-1;while(d>0&&b.digits[d]==0)--d;return d};this.hexToChar=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];}
  function c(a){this.digits=typeof a=="boolean"&&a==true?null:new Array(21);this.isNeg=false;}
  function d(a){var b=new c(true);return b.digits=a.slice(0),b.isNeg=false,b}
  function e(a,b){var d=new c(true),e=0,f=0,g;for(;f<a.digits.length;f++)g=a.digits[f]+b.digits[f]+e,d.digits[f]=g&65535,e=g>>>16;return d}
  function f(a,b){var d=new c(true),e=0,f=0,g;for(;f<a.digits.length;f++)g=a.digits[f]-b.digits[f]+e,d.digits[f]=g&65535,g<0?e=-1:e=0;return d}
  function g(a,b){var d=new c(true),e=0,f=a.digits.length,h=0,i=0,j;for(;h<f;h++)i=a.digits[h]*b+e,d.digits[h]=i&65535,e=i>>>16;return d}
  function h(a,b){var d=new c(true),e=0,f=0,g,h;for(;f<a.digits.length;f++)g=a.digits[f]*b.digits[0]+e,d.digits[f]=g&65535,e=g>>>16;for(h=1;h<b.digits.length;h++){e=0;for(f=0;f<a.digits.length;f++)g=d.digits[f+h]+a.digits[f]*b.digits[h]+e,d.digits[f+h]=g&65535,e=g>>>16}return d}
  function i(a,b){var c=0,d=a.length,e;for(e=0;e<d;++e)c<<=8,c|=a[e];return c%b}
  function j(a){this.e=0;this.m=null;this.chunkSize=0;this.radix=16;this.barrett=null;this.setPublic=function(a,b){this.e=parseInt(b,16);this.m=k.biFromHex(a);this.chunkSize=2*k.biHighIndex(this.m);};this.doPublic=function(a){return l(a,this.e,this.m)};this.encrypt=function(a){var b=this.chunkSize;var c=new Array();var d=a.length;var e=0;while(e<d){var f=new Array();var g=Math.min(d,e+b-11);var h=0;f[h++]=0;while(h<b-(g-e)-1)f[h++]=Math.floor(Math.random()*254)+1;f[h++]=0;for(var i=e;i<g;i++)f[h++]=a.charCodeAt(i);var j=m(f);var n=this.doPublic(j);var o=k.biToHex(n);c.push(o);e=g}
    return c.join(" ")};};
  function k(){a.call(this)}
  k.prototype=new a;var k=new k;
  function l(a,b,c){var d=new c.constructor(true),e=a,f=b,g=c,h=new c.constructor(true),i=new c.constructor(true);h.digits[0]=1;while((f&1)!=0)h=hMul(h,e,g),f>>>=1,e=hMul(e,e,g);return h}
  function hMul(a,b,m){return n(h(a,b),m)}
  function n(a,b){return o(a,b)}
  function o(a,b){return p(a,b)}
  function p(a,b){var d=new c(true);return d=a,d}
  function m(a){var b=new c(true);b.isNeg=false;var d=0,e=a.length,f=0,g=0,h=0;for(var i=e-1;i>=0;--i){f=a[i];b.digits[d++]=f}
    return b}
  exports.JSEncrypt=function(){this.setPublicKey=function(pem){var b64=pem.replace(/-----[^-]+-----/g,'').replace(/\s+/g,'');var der=r(b64);var seq=s(der);var bitStr=t(seq.value[1].value);var modulus=u(bitStr);var exponent=v(bitStr);var n=k.biToHex(modulus);var eHex=exponent.toString(16);this.rsaKey=new j();this.rsaKey.setPublic(n,eHex)};this.encrypt=function(str){return this.rsaKey.encrypt(str)};};
  function r(a){a=a.replace(/[^A-Za-z0-9\+\/\=]/g,"");var b=atob(a),c=new Array(b.length);for(var d=0;d<b.length;d++)c[d]=b.charCodeAt(d);return c}
  function s(a){var b={value:[]},i=0,len=a.length;function readLen(idx){var l=a[idx++];if(l<128)return [l,idx];var n=l&127;var v=0;for(var j=0;j<n;j++)v=(v<<8)|a[idx++];return [v,idx]}if(a[i++]!=48)return b;var rl=readLen(i);var end=i+rl[0];i=rl[1];while(i<end){var tag=a[i++];var rl2=readLen(i);var end2=i+rl2[0];var body=a.slice(rl2[1],end2);b.value.push({tag:tag,value:body});i=end2}return b}
  function t(a){return {value:a}}
  function u(bitstr){var a=bitstr.value;var i=0;if(a[i]==0)i++;return k.biFromHex(Array.prototype.map.call(a.slice(i).map? a.slice(i):Array.from(a.slice(i)),function(x){var h=x.toString(16);return (h.length==1?'0'+h:h)}).join(''))}
  function v(bitstr){var a=bitstr.value, val=0;for(var i=a.length-1;i>=0;i--)val|=a[i]<<((a.length-1-i)*8);return val}
})(JSEncryptExports);
module.exports = JSEncryptExports;


