const { RSA_PUBLIC_KEY_PEM } = require('./keys');
const JSE = require('./jsencrypt');

function encryptPhoneWithRSA(phone) {
  const jse = new JSE.JSEncrypt();
  jse.setPublicKey(RSA_PUBLIC_KEY_PEM);
  // 返回Hex串，这个轻量实现输出为十六进制。统一再转为Base64，便于传输。
  const hexCipher = jse.encrypt(String(phone));
  if (!hexCipher) return '';
  const base64 = hexToBase64(hexCipher);
  return base64;
}

function hexToBase64(hex) {
  const cleaned = hex.replace(/\s+/g, '');
  const bytes = cleaned.match(/.{1,2}/g).map(h => parseInt(h, 16));
  const bstr = String.fromCharCode.apply(null, bytes);
  if (typeof btoa !== 'undefined') return btoa(bstr);
  // WeChat may not have btoa; fallback
  return Buffer.from(bstr, 'binary').toString('base64');
}

module.exports = { encryptPhoneWithRSA };


