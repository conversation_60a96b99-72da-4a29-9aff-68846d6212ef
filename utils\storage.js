// utils/storage.js
// 简单的TTL本地存储封装：setWithTTL/getWithTTL/remove/clear

function setWithTTL(key, value, ttlMs) {
  const now = Date.now();
  const record = { value, expireAt: ttlMs ? now + ttlMs : null };
  try {
    wx.setStorageSync(key, record);
  } catch (e) {}
}

function getWithTTL(key) {
  try {
    const record = wx.getStorageSync(key);
    if (!record) return null;
    if (record && typeof record === 'object' && record.expireAt) {
      if (Date.now() > record.expireAt) {
        try { wx.removeStorageSync(key); } catch (e) {}
        return null;
      }
    }
    return record && typeof record === 'object' && 'value' in record ? record.value : record;
  } catch (e) {
    return null;
  }
}

function remove(key) {
  try { wx.removeStorageSync(key); } catch (e) {}
}

function clear() {
  try { wx.clearStorageSync(); } catch (e) {}
}

module.exports = { setWithTTL, getWithTTL, remove, clear };




